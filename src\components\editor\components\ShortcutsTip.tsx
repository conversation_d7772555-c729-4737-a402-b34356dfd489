import React, { useState } from "react";
import { Keyboard, X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface Shortcut {
  keys: string[];
  description: string;
  category: string;
}

const shortcuts: Shortcut[] = [
  // Basic editing
  { keys: ["Ctrl", "Z"], description: "Undo", category: "Basic" },
  { keys: ["Ctrl", "Y"], description: "Redo", category: "Basic" },
  { keys: ["Ctrl", "A"], description: "Select all", category: "Basic" },
  { keys: ["Ctrl", "C"], description: "Copy", category: "Basic" },
  { keys: ["Ctrl", "V"], description: "Paste", category: "Basic" },
  { keys: ["Ctrl", "X"], description: "Cut", category: "Basic" },

  // Commands
  { keys: ["/"], description: "Open command menu", category: "Commands" },
  { keys: ["Escape"], description: "Close command menu", category: "Commands" },
  { keys: ["↑", "↓"], description: "Navigate commands", category: "Commands" },
  { keys: ["Enter"], description: "Execute command", category: "Commands" },

  // Formatting
  { keys: ["Tab"], description: "Indent list item", category: "Lists" },
  {
    keys: ["Shift", "Tab"],
    description: "Outdent list item",
    category: "Lists",
  },

  // Table shortcuts
  { keys: ["Tab"], description: "Next table cell", category: "Tables" },
  {
    keys: ["Shift", "Tab"],
    description: "Previous table cell",
    category: "Tables",
  },
];

const ShortcutsTip: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const isMac =
    typeof navigator !== "undefined" &&
    navigator.platform.toUpperCase().indexOf("MAC") >= 0;

  // Handle Escape key to close modal
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [isOpen]);

  const categories = Array.from(new Set(shortcuts.map((s) => s.category)));

  const renderKey = (key: string) => {
    const displayKey = isMac && key === "Ctrl" ? "⌘" : key;
    return (
      <kbd
        key={key}
        className="px-1.5 py-0.5 text-xs font-mono bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded shadow-sm"
      >
        {displayKey}
      </kbd>
    );
  };

  const renderShortcut = (shortcut: Shortcut, index: number) => (
    <div
      key={index}
      className="flex items-center justify-between py-1.5 px-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
    >
      <span className="text-sm text-gray-700 dark:text-gray-300 flex-1">
        {shortcut.description}
      </span>
      <div className="flex items-center gap-1">
        {shortcut.keys.map((key, keyIndex) => (
          <React.Fragment key={keyIndex}>
            {keyIndex > 0 && (
              <span className="text-xs text-gray-400 mx-0.5">+</span>
            )}
            {renderKey(key)}
          </React.Fragment>
        ))}
      </div>
    </div>
  );

  return (
    <>
      {/* Trigger Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="fixed top-4 right-4 z-40 h-9 w-9 p-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/60 dark:border-gray-700/60 shadow-lg hover:shadow-xl transition-all duration-300 hover:bg-white dark:hover:bg-gray-800 hover:scale-105 group"
        title="Keyboard shortcuts (Click to view all shortcuts)"
      >
        <Keyboard className="h-4 w-4 text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors" />
      </Button>

      {/* Modal Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/20 dark:bg-black/40 backdrop-blur-sm"
            onClick={() => setIsOpen(false)}
          />

          {/* Modal Content */}
          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 w-full max-w-2xl max-h-[80vh] overflow-hidden mx-4">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2">
                <Keyboard className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Keyboard Shortcuts
                </h2>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-80px)]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {categories.map((category) => (
                  <div key={category} className="space-y-2">
                    <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-1">
                      {category}
                    </h3>
                    <div className="space-y-0.5">
                      {shortcuts
                        .filter((s) => s.category === category)
                        .map((shortcut, index) =>
                          renderShortcut(shortcut, index)
                        )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Footer tip */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
                <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  Press{" "}
                  <kbd className="px-1 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 rounded">
                    Escape
                  </kbd>{" "}
                  or click outside to close
                </p>
                {isMac && (
                  <p className="text-xs text-gray-400 dark:text-gray-500 text-center">
                    ⌘ represents Cmd key on Mac
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ShortcutsTip;
